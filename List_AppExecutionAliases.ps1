# List all registered App Execution Aliases for the current user
$aliasRoot = "HKCU:\Software\Classes\ActivatableClasses\AppExecutionAliasRegistration"

if (Test-Path $aliasRoot) {
    Get-ChildItem $aliasRoot | ForEach-Object {
        $alias = $_.PSChildName
        $props = Get-ItemProperty "$aliasRoot\$alias"
        [PSCustomObject]@{
            Alias         = $alias
            AUMID         = $props.AppUserModelID
            Package       = $props.PackageFamilyName
            DesktopAppID  = $props.DesktopAppID
        }
    } | Format-Table -AutoSize
} else {
    Write-Host "No aliases registered in this registry path."
}
