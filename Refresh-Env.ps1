<#
.SYNOPSIS
    Refreshes environment variables in the current PowerShell session.

.DESCRIPTION
    This script updates the current PowerShell session with the latest environment
    variables from the registry, including PATH and PSModulePath. It properly handles
    environment variable expansion, removes duplicates, and correctly merges User and
    System variables.

.NOTES
    File Name      : Refresh-Env.ps1
    Author         : AI Assistant
    Prerequisite   : PowerShell 5.1 or later
    Created Date   : 2025-05-05

.EXAMPLE
    PS> .\Refresh-Env.ps1
    
    Refreshes all environment variables in the current session

.EXAMPLE
    PS> .\Refresh-Env.ps1 -Verbose
    
    Refreshes all environment variables with detailed output about the changes

.EXAMPLE
    PS> .\Refresh-Env.ps1 -RemoveDuplicates
    
    Refreshes all environment variables and removes duplicates from PATH and PSModulePath
#>

[CmdletBinding()]
param (
    [Parameter(Mandatory = $false)]
    [switch]$RemoveDuplicates,
    
    [Parameter(Mandatory = $false)]
    [switch]$QuietMode
)

function Get-NormalizedPath {
    param (
        [string]$Path
    )
    
    # Normalize path by trimming trailing slashes and converting to lowercase
    # This helps identify duplicate paths regardless of trailing slash or case
    return $Path.TrimEnd('\').ToLower()
}

function Remove-PathDuplicates {
    param (
        [string[]]$Paths
    )
    
    $uniquePaths = @()
    $seen = @{}
    
    foreach ($path in $Paths) {
        # Skip empty entries
        if ([string]::IsNullOrWhiteSpace($path)) { continue }
        
        # Normalize path for comparison
        $normalizedPath = Get-NormalizedPath -Path $path
        if (-not $seen.ContainsKey($normalizedPath)) {
            $seen[$normalizedPath] = $true
            $uniquePaths += $path
        }
        else {
            Write-Verbose "Removing duplicate path: $path"
        }
    }
    
    return $uniquePaths
}

function Merge-EnvironmentPaths {
    param (
        [string]$UserPath,
        [string]$MachinePath,
        [switch]$RemoveDups = $RemoveDuplicates
    )
    
    # Split paths into arrays and filter out empty entries
    $userPaths = @()
    if (-not [string]::IsNullOrWhiteSpace($UserPath)) {
        $userPaths = $UserPath -split ';' | Where-Object { -not [string]::IsNullOrWhiteSpace($_) }
    }
    
    $machinePaths = @()
    if (-not [string]::IsNullOrWhiteSpace($MachinePath)) {
        $machinePaths = $MachinePath -split ';' | Where-Object { -not [string]::IsNullOrWhiteSpace($_) }
    }
    
    # Combine user and machine paths (user paths take precedence)
    $combinedPaths = $userPaths + $machinePaths
    
    # Remove duplicates if requested
    if ($RemoveDups) {
        $originalCount = $combinedPaths.Count
        $combinedPaths = Remove-PathDuplicates -Paths $combinedPaths
        $removedCount = $originalCount - $combinedPaths.Count
        if ($removedCount -gt 0) {
            Write-Verbose "Removed $removedCount duplicate path entries"
        }
    }
    
    return $combinedPaths -join ';'
}

# Save original values for important variables (to avoid unintended overrides)
Write-Verbose "Saving original values of critical environment variables"
$originalVars = @{
    "USERNAME" = $env:USERNAME
    "PROCESSOR_ARCHITECTURE" = $env:PROCESSOR_ARCHITECTURE
    "COMPUTERNAME" = $env:COMPUTERNAME
    "USERDOMAIN" = $env:USERDOMAIN
}

# Track changes for reporting
$changes = @{
    Added = @()
    Updated = @()
    PathSegments = @{
        Before = ($env:PATH -split ';').Count
        After = 0
    }
    PSModulePathSegments = @{
        Before = ($env:PSModulePath -split ';').Count
        After = 0
    }
}

# Start feedback message
if (-not $QuietMode) {
    Write-Host "Refreshing environment variables from registry..." -ForegroundColor Cyan
}

# Retrieve system environment variables from the registry
try {
    Write-Verbose "Reading system environment variables from registry"
    $systemRegKey = "Registry::HKLM\System\CurrentControlSet\Control\Session Manager\Environment"
    $systemEnv = Get-ItemProperty -Path $systemRegKey -ErrorAction Stop
} 
catch {
    Write-Warning ("Unable to read system environment variables from the registry: " + $_.Exception.Message)
    $systemEnv = [pscustomobject]@{}
}

# Retrieve user environment variables from the registry
try {
    Write-Verbose "Reading user environment variables from registry"
    $userRegKey = "Registry::HKCU\Environment"
    $userEnv = Get-ItemProperty -Path $userRegKey -ErrorAction Stop
} 
catch {
    Write-Warning ("Unable to read user environment variables from the registry: " + $_.Exception.Message)
    $userEnv = [pscustomobject]@{}
}

# Process all non-path environment variables
Write-Verbose "Processing regular environment variables"
$processedCount = 0

# Function to update environment variables
function Update-EnvironmentVariable {
    param (
        [string]$Name,
        [string]$Value
    )
    
    # Skip null/empty values and special variables
    if ([string]::IsNullOrWhiteSpace($Value) -or 
        $Name -in @("PSPath", "PSParentPath", "PSChildName", "PSDrive", "PSProvider", "Path", "PSModulePath")) {
        return $false
    }
    
    # Expand any environment variables in the value
    try {
        $expandedValue = [System.Environment]::ExpandEnvironmentVariables($Value)
        Write-Verbose ("Expanded '" + $Name + "' from '" + $Value + "' to '" + $expandedValue + "'")
    }
    catch {
        Write-Warning ("Failed to expand environment variables in " + $Name + ": " + $_.Exception.Message)
        $expandedValue = $Value
    }
    
    # Check if the variable exists and if its value is different
    $currentValue = $null
    try {
        $currentValue = [System.Environment]::GetEnvironmentVariable($Name)
    }
    catch {
        Write-Warning ("Failed to get current value for " + $Name + ": " + $_.Exception.Message)
    }
    
    if ($null -eq $currentValue) {
        # Variable doesn't exist in current session
        try {
            Set-Item -Path "env:$Name" -Value $expandedValue
            $script:changes.Added += $Name
            Write-Verbose ("Added variable: " + $Name + " = " + $expandedValue)
            return $true
        }
        catch {
            Write-Warning ("Failed to add variable " + $Name + ": " + $_.Exception.Message)
            return $false
        }
    }
    elseif ($currentValue -ne $expandedValue) {
        # Variable exists but value is different
        try {
            Set-Item -Path "env:$Name" -Value $expandedValue
            $script:changes.Updated += $Name
            Write-Verbose ("Updated variable: " + $Name + " from '" + $currentValue + "' to '" + $expandedValue + "'")
            return $true
        }
        catch {
            Write-Warning ("Failed to update variable " + $Name + ": " + $_.Exception.Message)
            return $false
        }
    }
    
    return $false
}

# Process system variables first
foreach ($property in $systemEnv.PSObject.Properties) {
    if (Update-EnvironmentVariable -Name $property.Name -Value $property.Value) {
        $processedCount++
    }
}

# Process user variables (which override system variables)
foreach ($property in $userEnv.PSObject.Properties) {
    if (Update-EnvironmentVariable -Name $property.Name -Value $property.Value) {
        $processedCount++
    }
}

# Special handling for PATH
Write-Verbose "Processing PATH variable"
$pathSystem = $systemEnv.Path
$pathUser = $userEnv.Path

# Expand environment variables in PATH
$expandedPathSystem = ""
$expandedPathUser = ""

if (-not [string]::IsNullOrWhiteSpace($pathSystem)) {
    try {
        $expandedPathSystem = [System.Environment]::ExpandEnvironmentVariables($pathSystem)
        Write-Verbose "Expanded System PATH variables"
        
        # Additional path-level expansion to catch any nested variables
        $expandedPaths = @()
        foreach ($path in $expandedPathSystem -split ';') {
            if (-not [string]::IsNullOrWhiteSpace($path)) {
                if ($path -like "*%*%*") {
                    $expanded = [System.Environment]::ExpandEnvironmentVariables($path)
                    Write-Verbose ("  Expanded path: " + $path + " -> " + $expanded)
                    $expandedPaths += $expanded
                } else {
                    $expandedPaths += $path
                }
            }
        }
        $expandedPathSystem = $expandedPaths -join ';'
    }
    catch {
        Write-Warning ("Failed to expand system PATH variables: " + $_.Exception.Message)
    }
}

if (-not [string]::IsNullOrWhiteSpace($pathUser)) {
    try {
        $expandedPathUser = [System.Environment]::ExpandEnvironmentVariables($pathUser)
        Write-Verbose "Expanded User PATH variables"
        
        # Additional path-level expansion to catch any nested variables
        $expandedPaths = @()
        foreach ($path in $expandedPathUser -split ';') {
            if (-not [string]::IsNullOrWhiteSpace($path)) {
                if ($path -like "*%*%*") {
                    $expanded = [System.Environment]::ExpandEnvironmentVariables($path)
                    Write-Verbose ("  Expanded path: " + $path + " -> " + $expanded)
                    $expandedPaths += $expanded
                } else {
                    $expandedPaths += $path
                }
            }
        }
        $expandedPathUser = $expandedPaths -join ';'
    }
    catch {
        Write-Warning ("Failed to expand user PATH variables: " + $_.Exception.Message)
    }
}

# Merge PATH values
$newPath = Merge-EnvironmentPaths -UserPath $expandedPathUser -MachinePath $expandedPathSystem
$env:PATH = $newPath
$changes.PathSegments.After = ($newPath -split ';').Count
$changes.Updated += "PATH"

# Special handling for PSModulePath
Write-Verbose "Processing PSModulePath variable"
$psModulePathSystem = $systemEnv.PSModulePath
$psModulePathUser = $userEnv.PSModulePath

# Expand environment variables in PSModulePath
$expandedPSModulePathSystem = ""
$expandedPSModulePathUser = ""

if (-not [string]::IsNullOrWhiteSpace($psModulePathSystem)) {
    try {
        $expandedPSModulePathSystem = [System.Environment]::ExpandEnvironmentVariables($psModulePathSystem)
        Write-Verbose "Expanded System PSModulePath variables"
        
        # Additional path-level expansion to catch any nested variables
        $expandedPaths = @()
        foreach ($path in $expandedPSModulePathSystem -split ';') {
            if (-not [string]::IsNullOrWhiteSpace($path)) {
                if ($path -like "*%*%*") {
                    $expanded = [System.Environment]::ExpandEnvironmentVariables($path)
                    Write-Verbose ("  Expanded PSModulePath: " + $path + " -> " + $expanded)
                    $expandedPaths += $expanded
                } else {
                    $expandedPaths += $path
                }
            }
        }
        $expandedPSModulePathSystem = $expandedPaths -join ';'
    }
    catch {
        Write-Warning ("Failed to expand system PSModulePath variables: " + $_.Exception.Message)
    }
}

if (-not [string]::IsNullOrWhiteSpace($psModulePathUser)) {
    try {
        $expandedPSModulePathUser = [System.Environment]::ExpandEnvironmentVariables($psModulePathUser)
        Write-Verbose "Expanded User PSModulePath variables"
        
        # Additional path-level expansion to catch any nested variables
        $expandedPaths = @()
        foreach ($path in $expandedPSModulePathUser -split ';') {
            if (-not [string]::IsNullOrWhiteSpace($path)) {
                if ($path -like "*%*%*") {
                    $expanded = [System.Environment]::ExpandEnvironmentVariables($path)
                    Write-Verbose ("  Expanded PSModulePath: " + $path + " -> " + $expanded)
                    $expandedPaths += $expanded
                } else {
                    $expandedPaths += $path
                }
            }
        }
        $expandedPSModulePathUser = $expandedPaths -join ';'
    }
    catch {
        Write-Warning ("Failed to expand user PSModulePath variables: " + $_.Exception.Message)
    }
}

# Merge PSModulePath values
$newPSModulePath = Merge-EnvironmentPaths -UserPath $expandedPSModulePathUser -MachinePath $expandedPSModulePathSystem
$env:PSModulePath = $newPSModulePath
$changes.PSModulePathSegments.After = ($newPSModulePath -split ';').Count
$changes.Updated += "PSModulePath"

# Restore original values for critical variables
Write-Verbose "Restoring original values for critical environment variables"
foreach ($key in $originalVars.Keys) {
    if (-not [string]::IsNullOrEmpty($originalVars[$key])) {
        Set-Item -Path "env:$key" -Value $originalVars[$key]
    }
}

# Display summary unless quiet mode is enabled
if (-not $QuietMode) {
    Write-Host "Environment refresh completed!" -ForegroundColor Green
    Write-Host "  Added: $($changes.Added.Count) new variables" -ForegroundColor Cyan
    Write-Host "  Updated: $($changes.Updated.Count - 2) existing variables" -ForegroundColor Cyan
    Write-Host "  PATH: $($changes.PathSegments.Before) → $($changes.PathSegments.After) segments" -ForegroundColor Cyan
    Write-Host "  PSModulePath: $($changes.PSModulePathSegments.Before) → $($changes.PSModulePathSegments.After) segments" -ForegroundColor Cyan
    
    if ($VerbosePreference -eq 'Continue') {
        if ($changes.Added.Count -gt 0) {
            Write-Host "`nAdded variables:" -ForegroundColor Yellow
            foreach ($var in $changes.Added) {
                # Use proper syntax for dynamic variable references
                $value = & { Get-Item -Path "env:$var" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty Value }
                Write-Host "  $var = $value"
            }
        }
        
        if ($changes.Updated.Count -gt 2) { # Exclude PATH and PSModulePath which are always counted as updated
            Write-Host "`nUpdated variables:" -ForegroundColor Yellow
            foreach ($var in $changes.Updated) {
                if ($var -notin @('PATH', 'PSModulePath')) {
                    # Use proper syntax for dynamic variable references
                    $value = & { Get-Item -Path "env:$var" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty Value }
                    Write-Host "  $var = $value"
                }
            }
        }
    }
}

# Helper function to display the state of PATH and PSModulePath
if ($VerbosePreference -eq 'Continue') {
    Write-Host "`nFinal PATH entries (first 5 or 99):" -ForegroundColor Yellow
    ($env:PATH -split ';' | Where-Object { -not [string]::IsNullOrWhiteSpace($_) } | Select-Object -First 99) | 
        ForEach-Object { Write-Host "  $_" }
        
    Write-Host "`nFinal PSModulePath entries (first 3 or 99):" -ForegroundColor Yellow
    ($env:PSModulePath -split ';' | Where-Object { -not [string]::IsNullOrWhiteSpace($_) } | Select-Object -First 99) | 
        ForEach-Object { Write-Host "  $_" }
}

# Return the results
return @{
    Success = $true
    Changes = $changes
}
