<#
.SYNOPSIS
    Backup and restore PATH and PSModulePath environment variables.

.DESCRIPTION
    This script provides functions to backup and restore PATH and PSModulePath
    environment variables for both the current user and system. It includes
    capability to store the current optimized values and restore them if needed.

.NOTES
    File Name      : Backup-EnvVariables.ps1
    Author         : AI Assistant
    Prerequisite   : PowerShell 5.1 or later
    Created Date   : 2025-05-04

.EXAMPLE
    PS> . .\Backup-EnvVariables.ps1
    PS> Backup-EnvVariables
    
    Creates a backup of the current PATH and PSModulePath variables

.EXAMPLE
    PS> . .\Backup-EnvVariables.ps1
    PS> Restore-EnvVariables -BackupFile "C:\Temp\env_backup_20250504.xml"
    
    Restores PATH and PSModulePath variables from the specified backup file
#>

#Requires -Version 5.1
#Requires -RunAsAdministrator

# Current optimized values as of 2025-05-04
$OPTIMIZED_PATH = @(
    "C:\Python313\Scripts",
    "C:\Python313",
    "$env:USERPROFILE\AppData\Roaming\Python\Python313\Scripts",
    "$env:USERPROFILE\AppData\Local\fnm_multishells\15008_1746169212623",
    "$env:USERPROFILE\.cargo\bin",
    "$env:USERPROFILE\.cargo\binstall",
    "$env:USERPROFILE\.dotnet\tools",
    "$env:USERPROFILE\scoop\shims",
    "$env:USERPROFILE\AppData\Local\Microsoft\WinGet\Links",
    "$env:USERPROFILE\AppData\Local\Microsoft\WinGet\Packages\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe",
    "$env:USERPROFILE\AppData\Local\Microsoft\WindowsApps",
    "$env:USERPROFILE\AppData\Local\GitHubDesktop\bin",
    "$env:USERPROFILE\go\bin",
    "$env:USERPROFILE\AppData\Roaming\npm",
    "C:\Program Files\PowerShell\7",
    "C:\ProgramData\PowerShellUniversal\Server",
    "C:\WINDOWS\System32\WindowsPowerShell\v1.0",
    "C:\Program Files (x86)\oh-my-posh\bin",
    "C:\Program Files (x86)\Common Files\Oracle\Java\java8path",
    "C:\Repos\vcpkg",
    "C:\ProgramData\chocolatey\bin",
    "C:\Program Files\dotnet",
    "C:\Program Files\Git\cmd",
    "C:\Program Files\Git\usr\bin",
    "C:\Program Files\GnuPG\bin",
    "C:\Program Files\Go\bin",
    "C:\Program Files\Microsoft VS Code\bin",
    "C:\Program Files\nodejs",
    "C:\Program Files\PuTTY",
    "C:\Program Files (x86)\Universal\Modules",
    "C:\WINDOWS\System32\OpenSSH",
    "C:\WINDOWS\System32\Wbem",
    "C:\WINDOWS\system32",
    "C:\WINDOWS",
    "C:\msys64\ucrt64\bin",
    "C:\Scripts"
) -join ';'

$OPTIMIZED_PSMODULEPATH = @(
    "$env:USERPROFILE\Documents\PowerShell\Modules",
    "$env:USERPROFILE\Documents\WindowsPowerShell\Modules",
    "C:\Program Files\PowerShell\7\Modules",
    "C:\Program Files\WindowsPowerShell\Modules",
    "C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules",
    "C:\Program Files (x86)\Universal\Modules",
    "C:\Program Files (x86)\Universal\Modules\Universal.Rules"
) -join ';'

function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

function Get-TimeStamp {
    return Get-Date -Format "yyyyMMdd_HHmmss"
}

function Backup-EnvVariables {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $false)]
        [string]$BackupPath = "$env:USERPROFILE\Documents\EnvBackups",
        
        [Parameter(Mandatory = $false)]
        [string]$BackupFileName = "env_backup_$(Get-TimeStamp).xml",
        
        [Parameter(Mandatory = $false)]
        [switch]$IncludeAllVariables
    )
    
    try {
        # Create backup directory if it doesn't exist
        if (-not (Test-Path -Path $BackupPath)) {
            New-Item -Path $BackupPath -ItemType Directory -Force | Out-Null
            Write-Host "Created backup directory: $BackupPath" -ForegroundColor Green
        }
        
        $backupFile = Join-Path -Path $BackupPath -ChildPath $BackupFileName
        
        $backup = @{
            CreationDate = Get-Date
            ComputerName = $env:COMPUTERNAME
            UserName = $env:USERNAME
            PowerShellVersion = $PSVersionTable.PSVersion.ToString()
            EnvironmentVariables = @{
                PATH = @{
                    User = [System.Environment]::GetEnvironmentVariable('PATH', 'User')
                    Machine = [System.Environment]::GetEnvironmentVariable('PATH', 'Machine')
                    Process = $env:PATH
                }
                PSModulePath = @{
                    User = [System.Environment]::GetEnvironmentVariable('PSModulePath', 'User')
                    Machine = [System.Environment]::GetEnvironmentVariable('PSModulePath', 'Machine')
                    Process = $env:PSModulePath
                }
            }
        }
        
        # Include all environment variables if requested
        if ($IncludeAllVariables) {
            $allVars = @{}
            [System.Environment]::GetEnvironmentVariables('User').GetEnumerator() | ForEach-Object {
                if ($_.Key -notin @('PATH', 'PSModulePath')) {
                    $allVars[$_.Key] = $_.Value
                }
            }
            $backup.EnvironmentVariables.Additional = $allVars
        }
        
        # Export to XML
        $backup | Export-Clixml -Path $backupFile -Force
        
        Write-Host "Environment variables successfully backed up to $backupFile" -ForegroundColor Green
        return $backupFile
    }
    catch {
        Write-Error "Failed to backup environment variables: $_"
        return $null
    }
}

function Restore-EnvVariables {
    [CmdletBinding(SupportsShouldProcess = $true, ConfirmImpact = 'High')]
    param (
        [Parameter(Mandatory = $false, Position = 0)]
        [string]$BackupFile,
        
        [Parameter(Mandatory = $false)]
        [switch]$RestoreOptimizedValues,
        
        [Parameter(Mandatory = $false)]
        [switch]$UserOnly,
        
        [Parameter(Mandatory = $false)]
        [switch]$Force
    )
    
    # Make sure we're running as admin if trying to set machine-level variables
    if (-not $UserOnly -and -not (Test-Administrator) -and -not $Force) {
        Write-Error "Administrator privileges required to set machine-level environment variables. Use -UserOnly to set user-level variables only, or restart PowerShell as Administrator."
        return $false
    }
    
    try {
        # If RestoreOptimizedValues is specified, use the optimized values
        if ($RestoreOptimizedValues) {
            $shouldContinue = $Force -or $PSCmdlet.ShouldContinue(
                "Are you sure you want to restore the predefined optimized PATH and PSModulePath values?",
                "This will overwrite your current user-level environment variables"
            )
            
            if ($shouldContinue) {
                [System.Environment]::SetEnvironmentVariable('PATH', $OPTIMIZED_PATH, 'User')
                [System.Environment]::SetEnvironmentVariable('PSModulePath', $OPTIMIZED_PSMODULEPATH, 'User')
                
                # Also update the current session
                $env:PATH = $OPTIMIZED_PATH
                $env:PSModulePath = $OPTIMIZED_PSMODULEPATH
                
                Write-Host "Successfully restored optimized environment variables." -ForegroundColor Green
                Write-Host "Changes will take effect in new PowerShell/command prompt windows." -ForegroundColor Yellow
                return $true
            }
            return $false
        }
        
        # If BackupFile is specified, restore from the backup file
        if (-not [string]::IsNullOrEmpty($BackupFile)) {
            if (-not (Test-Path -Path $BackupFile)) {
                Write-Error "Backup file not found: $BackupFile"
                return $false
            }
            
            $backup = Import-Clixml -Path $BackupFile
            
            # Verify backup content
            if (-not $backup.EnvironmentVariables.PATH -or -not $backup.EnvironmentVariables.PSModulePath) {
                Write-Error "Invalid backup file format. Required environment variables not found."
                return $false
            }
            
            $shouldContinue = $Force -or $PSCmdlet.ShouldContinue(
                "Are you sure you want to restore PATH and PSModulePath from backup created on $($backup.CreationDate)?",
                "This will overwrite your current environment variables"
            )
            
            if ($shouldContinue) {
                # Restore User environment variables
                [System.Environment]::SetEnvironmentVariable('PATH', $backup.EnvironmentVariables.PATH.User, 'User')
                [System.Environment]::SetEnvironmentVariable('PSModulePath', $backup.EnvironmentVariables.PSModulePath.User, 'User')
                
                # Restore Machine environment variables if requested
                if (-not $UserOnly -and (Test-Administrator)) {
                    [System.Environment]::SetEnvironmentVariable('PATH', $backup.EnvironmentVariables.PATH.Machine, 'Machine')
                    [System.Environment]::SetEnvironmentVariable('PSModulePath', $backup.EnvironmentVariables.PSModulePath.Machine, 'Machine')
                }
                
                # Update current session
                $env:PATH = $backup.EnvironmentVariables.PATH.Process
                $env:PSModulePath = $backup.EnvironmentVariables.PSModulePath.Process
                
                Write-Host "Successfully restored environment variables from backup." -ForegroundColor Green
                Write-Host "Backup date: $($backup.CreationDate)" -ForegroundColor Cyan
                Write-Host "Changes will take full effect in new PowerShell/command prompt windows." -ForegroundColor Yellow
                
                return $true
            }
            return $false
        }
        
        # If neither RestoreOptimizedValues nor BackupFile is specified, show available backups
        $backupPath = "$env:USERPROFILE\Documents\EnvBackups"
        if (Test-Path -Path $backupPath) {
            $backups = Get-ChildItem -Path $backupPath -Filter "env_backup_*.xml" | 
                       Sort-Object LastWriteTime -Descending
            
            if ($backups.Count -gt 0) {
                Write-Host "Available backups:" -ForegroundColor Cyan
                for ($i = 0; $i -lt [Math]::Min($backups.Count, 5); $i++) {
                    Write-Host "[$i] $($backups[$i].Name) - $($backups[$i].LastWriteTime)" -ForegroundColor Yellow
                }
                
                Write-Host "`nTo restore a backup, run:" -ForegroundColor Green
                Write-Host "Restore-EnvVariables -BackupFile `"$($backups[0].FullName)`"" -ForegroundColor Green
                
                Write-Host "`nTo restore optimized values, run:" -ForegroundColor Green
                Write-Host "Restore-EnvVariables -RestoreOptimizedValues" -ForegroundColor Green
            }
            else {
                Write-Host "No backups found in $backupPath" -ForegroundColor Yellow
                Write-Host "To restore optimized values, run:" -ForegroundColor Green
                Write-Host "Restore-EnvVariables -RestoreOptimizedValues" -ForegroundColor Green
            }
        }
        else {
            Write-Host "Backup directory not found: $backupPath" -ForegroundColor Yellow
            Write-Host "To restore optimized values, run:" -ForegroundColor Green
            Write-Host "Restore-EnvVariables -RestoreOptimizedValues" -ForegroundColor Green
        }
        
        return $null
    }
    catch {
        Write-Error "Failed to restore environment variables: $_"
        return $false
    }
}

function Reset-PathDuplicates {
    [CmdletBinding(SupportsShouldProcess = $true, ConfirmImpact = 'Medium')]
    param (
        [Parameter(Mandatory = $false)]
        [switch]$UserOnly,
        
        [Parameter(Mandatory = $false)]
        [switch]$Force
    )
    
    try {
        # Get current PATH
        $userPath = [System.Environment]::GetEnvironmentVariable('PATH', 'User')
        $machinePath = [System.Environment]::GetEnvironmentVariable('PATH', 'Machine')
        
        # Split into arrays and remove empty entries
        $userPathArray = $userPath -split ';' | Where-Object { -not [string]::IsNullOrWhiteSpace($_) }
        $machinePathArray = $machinePath -split ';' | Where-Object { -not [string]::IsNullOrWhiteSpace($_) }
        
        # Remove duplicates while preserving order
        $uniqueUserPath = @()
        $seenUser = @{}
        foreach ($path in $userPathArray) {
            $normalizedPath = $path.TrimEnd('\').ToLower()
            if (-not $seenUser.ContainsKey($normalizedPath)) {
                $seenUser[$normalizedPath] = $true
                $uniqueUserPath += $path
            }
        }
        
        $uniqueMachinePath = @()
        $seenMachine = @{}
        foreach ($path in $machinePathArray) {
            $normalizedPath = $path.TrimEnd('\').ToLower()
            if (-not $seenMachine.ContainsKey($normalizedPath)) {
                $seenMachine[$normalizedPath] = $true
                $uniqueMachinePath += $path
            }
        }
        
        # Report findings
        $userDuplicates = $userPathArray.Count - $uniqueUserPath.Count
        $machineDuplicates = $machinePathArray.Count - $uniqueMachinePath.Count
        
        Write-Host "Found $userDuplicates duplicate entries in User PATH" -ForegroundColor Yellow
        Write-Host "Found $machineDuplicates duplicate entries in Machine PATH" -ForegroundColor Yellow
        
        if ($userDuplicates -eq 0 -and ($machineDuplicates -eq 0 -or $UserOnly)) {
            Write-Host "No duplicate entries found to remove." -ForegroundColor Green
            return $true
        }
        
        # Confirm before making changes
        $shouldContinue = $Force -or $PSCmdlet.ShouldContinue(
            "Do you want to remove duplicate PATH entries?",
            "This will modify your environment variables"
        )
        
        if (-not $shouldContinue) {
            Write-Host "Operation cancelled by user." -ForegroundColor Yellow
            return $false
        }
        
        # Update User PATH
        if ($userDuplicates -gt 0) {
            $newUserPath = $uniqueUserPath -join ';'
            
            if ($PSCmdlet.ShouldProcess("User PATH Environment Variable", "Remove $userDuplicates duplicate entries")) {
                [System.Environment]::SetEnvironmentVariable('PATH', $newUserPath, 'User')
                Write-Host "Successfully removed $userDuplicates duplicate entries from User PATH" -ForegroundColor Green
            }
        }
        
        # Update Machine PATH if not UserOnly and have admin rights
        if ($machineDuplicates -gt 0 -and -not $UserOnly) {
            if (Test-Administrator) {
                $newMachinePath = $uniqueMachinePath -join ';'
                
                if ($PSCmdlet.ShouldProcess("Machine PATH Environment Variable", "Remove $machineDuplicates duplicate entries")) {
                    [System.Environment]::SetEnvironmentVariable('PATH', $newMachinePath, 'Machine')
                    Write-Host "Successfully removed $machineDuplicates duplicate entries from Machine PATH" -ForegroundColor Green
                }
            }
            else {
                Write-Warning "Administrator privileges required to update Machine PATH. Use -UserOnly to update only User PATH."
            }
        }
        
        # Update current session PATH
        $processPath = ($env:PATH -split ';' | Where-Object { -not [string]::IsNullOrWhiteSpace($_) } | Select-Object -Unique) -join ';'
        $env:PATH = $processPath
        
        Write-Host "Current session PATH has been updated." -ForegroundColor Green
        Write-Host "Note: For changes to take full effect, please restart any open applications or terminals." -ForegroundColor Yellow
        
        return $true
    }
    catch {
        Write-Error "Failed to remove PATH duplicates: $_"
        return $false
    }
}

# Export functions if being imported as a module
Export-ModuleMember -Function Backup-EnvVariables, Restore-EnvVariables, Reset-PathDuplicates

# Display usage information when script is run directly
if ($MyInvocation.InvocationName -eq $MyInvocation.MyCommand.Name) {
    Write-Host "Environment Variable Backup & Restore Utility" -ForegroundColor Cyan
    Write-Host "==============================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "This script provides functions to manage your PATH and PSModulePath environment variables." -ForegroundColor White
    Write-Host ""
    Write-Host "Available functions:" -ForegroundColor Green
    Write-Host "1. Backup-EnvVariables - Creates a backup of your environment variables" -ForegroundColor White
    Write-Host "2. Restore-EnvVariables - Restores environment variables from a backup" -ForegroundColor White
    Write-Host "3. Reset-PathDuplicates - Removes duplicate entries from your PATH" -ForegroundColor White
    Write-Host ""
    Write-Host "Example usage:" -ForegroundColor Yellow
    Write-Host "PS> . .\Backup-EnvVariables.ps1" -ForegroundColor White
    Write-Host "PS> Backup-EnvVariables" -ForegroundColor White
    Write-Host "PS> Reset-PathDuplicates -UserOnly" -ForegroundColor White
    Write-Host "PS> Restore-EnvVariables -RestoreOptimizedValues" -ForegroundColor White
}
