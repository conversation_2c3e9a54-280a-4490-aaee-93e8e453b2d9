# find_oh-my-posh_in_profiles.ps1
# Script to find Oh My Posh initialization in all PowerShell profile files
# Created: 2025-Jul-07 07:05 by <PERSON> (for <PERSON>)
# Copyright (c) 2025 <PERSON>

# Define all possible profile paths
$profilePaths = @(
    $PROFILE.AllUsersAllHosts,
    $PROFILE.AllUsersCurrentHost,
    $PROFILE.CurrentUserAllHosts,
    $PROFILE.CurrentUserCurrentHost
)

# Add VSCode profile paths if they might exist
$vscodeProfilePaths = @(
    [System.IO.Path]::Combine([System.IO.Path]::GetDirectoryName($PROFILE), "Microsoft.VSCode_profile.ps1")
)
if ($PSVersionTable.PSEdition -eq 'Core') {
    $windowsPSVSCodePath = $vscodeProfilePaths[0] -replace 'PowerShell', 'WindowsPowerShell'
    $vscodeProfilePaths += $windowsPSVSCodePath
}

$profilePaths += $vscodeProfilePaths

Write-Host "Searching for Oh My Posh initialization in profile files..." -ForegroundColor Cyan
$foundInitialization = $false

foreach ($path in $profilePaths) {
    if (Test-Path $path) {
        $content = Get-Content $path
        $lineNumber = 0
        
        foreach ($line in $content) {
            $lineNumber++
            if ($line -match "oh-my-posh init") {
                $foundInitialization = $true
                Write-Host "`nOh My Posh initialization found in: " -ForegroundColor Green -NoNewline
                Write-Host "${path}" -ForegroundColor Yellow
                Write-Host "Line ${lineNumber}: " -ForegroundColor Green -NoNewline
                Write-Host "${line}" -ForegroundColor Yellow
            }
        }
    }
}

if (-not $foundInitialization) {
    Write-Host "`nNo Oh My Posh initialization found in any profile files." -ForegroundColor Red
}

# Check if POSH_THEME_NAME environment variable is set
Write-Host "`nChecking environment variables..." -ForegroundColor Cyan
if ($env:POSH_THEME_NAME) {
    Write-Host "POSH_THEME_NAME is set to: " -ForegroundColor Green -NoNewline
    Write-Host "${env:POSH_THEME_NAME}" -ForegroundColor Yellow
    
    # Check if the theme file exists
    $themePath = "${env:POSH_THEMES_PATH}\${env:POSH_THEME_NAME}"
    if (Test-Path $themePath) {
        Write-Host "Theme file exists at: " -ForegroundColor Green -NoNewline
        Write-Host "${themePath}" -ForegroundColor Yellow
    } else {
        Write-Host "Theme file does NOT exist at: " -ForegroundColor Red -NoNewline
        Write-Host "${themePath}" -ForegroundColor Yellow
    }
} else {
    Write-Host "POSH_THEME_NAME environment variable is NOT set." -ForegroundColor Red
}

# Check POSH_THEMES_PATH
if ($env:POSH_THEMES_PATH) {
    Write-Host "`nPOSH_THEMES_PATH is set to: " -ForegroundColor Green -NoNewline
    Write-Host "${env:POSH_THEMES_PATH}" -ForegroundColor Yellow
    
    if (Test-Path $env:POSH_THEMES_PATH) {
        Write-Host "Directory exists." -ForegroundColor Green
        
        # List available themes
        $themeCount = (Get-ChildItem -Path $env:POSH_THEMES_PATH -Filter "*.omp.json" | Measure-Object).Count
        Write-Host "Found ${themeCount} theme files in this directory." -ForegroundColor Green
    } else {
        Write-Host "Directory does NOT exist!" -ForegroundColor Red
    }
} else {
    Write-Host "`nPOSH_THEMES_PATH environment variable is NOT set." -ForegroundColor Red
}

Write-Host "`nScript completed." -ForegroundColor Cyan
