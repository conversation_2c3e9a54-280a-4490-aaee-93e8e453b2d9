# Define the shared module path
$sharedPath = "C:\Program Files\PowerShell\Modules"

# Function to safely add a path to $env:PSModulePath if missing
function Add-ToModulePath {
    param ($path)
    if (-not ($env:PSModulePath -split ';' | Where-Object { $_ -eq $path })) {
        $env:PSModulePath += ";$path"
    }
}

# Add shared path to current session
Add-ToModulePath -path $sharedPath

# Optionally persist to profile
$persist = Read-Host "Do you want to persist this to your profile? (y/n)"
if ($persist -eq 'y') {
    $profileLines = @()
    if (Test-Path $PROFILE) {
        $profileLines = Get-Content $PROFILE
    }

    $marker = "# Added by PSModulePath sync script"
    if (-not ($profileLines -contains $marker)) {
        Add-Content $PROFILE "`n$marker"
        Add-Content $PROFILE "if (-not (`$env:PSModulePath -split ';' | Where-Object { `$_ -eq '$sharedPath' })) {"
        Add-Content $PROFILE "    `$env:PSModulePath += ';$sharedPath'"
        Add-Content $PROFILE "}"
    }

    Write-Host "✅ Shared path added to $PROFILE"
} else {
    Write-Host "❗Change is only for this session."
}

# Optional: show current PSModulePath
Write-Host "`nCurrent PSModulePath:`n"
$env:PSModulePath -split ';' | ForEach-Object { " - $_" }

# Optional copy function (for compatible modules only)
function Copy-UserModulesToShared {
    $userPath = Join-Path $HOME "Documents\WindowsPowerShell\Modules"
    if (-not (Test-Path $userPath)) {
        Write-Host "User module path does not exist: $userPath"
        return
    }

    Get-ChildItem -Directory $userPath | ForEach-Object {
        $dest = Join-Path $sharedPath $_.Name
        if (-not (Test-Path $dest)) {
            Copy-Item $_.FullName -Destination $sharedPath -Recurse
            Write-Host "✅ Copied module '$($_.Name)' to shared path."
        } else {
            Write-Host "⚠️ Module '$($_.Name)' already exists in shared path."
        }
    }
}

$sync = Read-Host "Do you want to copy user modules to shared path? (y/n)"
if ($sync -eq 'y') {
    Copy-UserModulesToShared
}
