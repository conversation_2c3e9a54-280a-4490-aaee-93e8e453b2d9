Write-Host "Checking Python installation..." -ForegroundColor Cyan

$pythonPath = Get-Command python -ErrorAction SilentlyContinue
if (-not $pythonPath) {
    Write-Host "❌ Python not found in PATH." -ForegroundColor Red
    exit 1
}

python --version
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to run python --version." -ForegroundColor Red
    exit 1
}

Write-Host "✅ Python is installed and working." -ForegroundColor Green

Write-Host "Checking pip..." -ForegroundColor Cyan
$pipCheck = & pip --version 2>$null
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ pip is not available." -ForegroundColor Red
    exit 1
}

Write-Host "✅ pip is working." -ForegroundColor Green
Write-Host "Installed packages:" -ForegroundColor Cyan
pip list
