# Select-Logfile.ps1
# Script to select and display log files sorted by latest first

param(
    [string]$Path = ".",
    [string[]]$Extensions = @("*.log", "*.txt", "*.out"),
    [int]$MaxLines = 100,
    [switch]$Tail,
    [switch]$Follow,
    [switch]$Recursive
)

# Function to format file size
function Format-FileSize {
    param([long]$Size)
    
    if ($Size -gt 1GB) {
        return "{0:N2} GB" -f ($Size / 1GB)
    } elseif ($Size -gt 1MB) {
        return "{0:N2} MB" -f ($Size / 1MB)
    } elseif ($Size -gt 1KB) {
        return "{0:N2} KB" -f ($Size / 1KB)
    } else {
        return "{0} bytes" -f $Size
    }
}

# Function to display file content
function Show-LogContent {
    param(
        [string]$FilePath,
        [int]$Lines,
        [bool]$TailMode,
        [bool]$FollowMode
    )
    
    Write-Host "`n" + ("=" * 80) -ForegroundColor Cyan
    Write-Host "Displaying: $FilePath" -ForegroundColor Yellow
    Write-Host ("=" * 80) -ForegroundColor Cyan
    
    if ($FollowMode) {
        Write-Host "Following log file (Press Ctrl+C to stop)..." -ForegroundColor Green
        Get-Content $FilePath -Wait -Tail $Lines
    } elseif ($TailMode) {
        Write-Host "Last $Lines lines:" -ForegroundColor Green
        Get-Content $FilePath -Tail $Lines
    } else {
        Write-Host "First $Lines lines:" -ForegroundColor Green
        Get-Content $FilePath -TotalCount $Lines
    }
}

# Resolve the path
$SearchPath = Resolve-Path $Path -ErrorAction SilentlyContinue
if (-not $SearchPath) {
    Write-Host "Path not found: $Path" -ForegroundColor Red
    exit 1
}

Write-Host "Searching for log files in: $SearchPath" -ForegroundColor Cyan
if ($Recursive) {
    Write-Host "Searching recursively..." -ForegroundColor Gray
}

# Find log files
$logFiles = @()
foreach ($ext in $Extensions) {
    if ($Recursive) {
        $files = Get-ChildItem -Path $SearchPath -Filter $ext -File -Recurse -ErrorAction SilentlyContinue
    } else {
        $files = Get-ChildItem -Path $SearchPath -Filter $ext -File -ErrorAction SilentlyContinue
    }
    $logFiles += $files
}

# Remove duplicates and sort by last write time (newest first)
$logFiles = $logFiles | Sort-Object FullName -Unique | Sort-Object LastWriteTime -Descending

if ($logFiles.Count -eq 0) {
    Write-Host "No log files found matching extensions: $($Extensions -join ', ')" -ForegroundColor Yellow
    Write-Host "Searched in: $SearchPath" -ForegroundColor Gray
    exit 0
}

# Display header
$separator = "=" * 80
Write-Host $separator -ForegroundColor Cyan
Write-Host "Log File Selector" -ForegroundColor Cyan
Write-Host $separator -ForegroundColor Cyan
Write-Host ""

Write-Host "Found $($logFiles.Count) log file(s):" -ForegroundColor Green
Write-Host ""

# Display files with numbering
for ($i = 0; $i -lt $logFiles.Count; $i++) {
    $file = $logFiles[$i]
    $fileSize = Format-FileSize $file.Length
    $lastModified = $file.LastWriteTime.ToString("yyyy-MM-dd HH:mm:ss")
    
    $indexText = "{0,3}. " -f ($i + 1)
    $nameText = "{0,-40}" -f $file.Name
    $sizeText = "{0,10}" -f $fileSize
    $dateText = " {0}" -f $lastModified
    
    Write-Host $indexText -NoNewline -ForegroundColor White
    Write-Host $nameText -NoNewline -ForegroundColor Yellow
    Write-Host $sizeText -NoNewline -ForegroundColor Cyan
    Write-Host $dateText -ForegroundColor Gray
}

Write-Host ""
Write-Host "Options:" -ForegroundColor Cyan
Write-Host "  Enter number to view file" -ForegroundColor White
Write-Host "  'q' or 'quit' to exit" -ForegroundColor White
Write-Host "  'r' or 'refresh' to refresh the list" -ForegroundColor White

if ($Tail) {
    Write-Host "  Tail mode: Will show last $MaxLines lines" -ForegroundColor Green
}
if ($Follow) {
    Write-Host "  Follow mode: Will follow the selected file" -ForegroundColor Green
}

Write-Host ""

# Get user selection
do {
    $selection = Read-Host "Enter your choice"
    
    # Handle quit
    if ($selection -eq 'q' -or $selection -eq 'quit' -or $selection -eq 'Q') {
        Write-Host "Exiting..." -ForegroundColor Yellow
        exit 0
    }
    
    # Handle refresh
    if ($selection -eq 'r' -or $selection -eq 'refresh' -or $selection -eq 'R') {
        Write-Host "Refreshing file list..." -ForegroundColor Yellow
        # Re-run the script
        & $PSCommandPath @PSBoundParameters
        exit 0
    }
    
    # Try to parse as integer
    $fileIndex = 0
    if ([int]::TryParse($selection, [ref]$fileIndex)) {
        # Convert to zero-based index
        $fileIndex = $fileIndex - 1
        
        if ($fileIndex -ge 0 -and $fileIndex -lt $logFiles.Count) {
            $selectedFile = $logFiles[$fileIndex]
            
            # Check if file still exists
            if (Test-Path $selectedFile.FullName) {
                Show-LogContent -FilePath $selectedFile.FullName -Lines $MaxLines -TailMode $Tail -FollowMode $Follow
                
                if (-not $Follow) {
                    Write-Host "`nPress any key to return to file list..." -ForegroundColor Yellow
                    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
                    Write-Host "`nReturning to file list..." -ForegroundColor Green
                }
            } else {
                Write-Host "File no longer exists: $($selectedFile.FullName)" -ForegroundColor Red
                Write-Host "Try refreshing the list with 'r'" -ForegroundColor Yellow
            }
        } else {
            Write-Host "Invalid selection. Please enter a number between 1 and $($logFiles.Count)." -ForegroundColor Red
        }
    } else {
        Write-Host "Invalid input. Please enter a number, 'q' to quit, or 'r' to refresh." -ForegroundColor Red
    }
    
    Write-Host ""
} while ($true)
