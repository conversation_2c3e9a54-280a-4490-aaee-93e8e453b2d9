#!/bin/bash

# Set timestamp and paths
timestamp=$(date '+%Y%b%d_%H%M')
logdir="$HOME/logs"
logfile="$logdir/gpg_keyboxd_versions_${timestamp}.json"

# Ensure log directory exists
if [[ ! -d "$logdir" ]]; then
    mkdir -p "$logdir"
fi

# Initialize JSON array and counters
json_output="["
found_count=0
not_found_count=0

# GitKraken versions
GITKRAKEN_VERSIONS=("11.2.0" "11.1.1")

GPG_PATHS=(
    "/c/Program Files/GnuPG/bin"
    "/c/Program Files (x86)/GnuPG/bin"
    "/c/Program Files/Git/usr/bin"
    "/c/msys64/usr/bin"
)

KEYBOXD_PATHS=(
    "/c/Program Files/GnuPG/bin"
    "/c/Program Files (x86)/GnuPG/bin"
    "/c/Program Files/Git/usr/lib/gnupg"
)

# Helper: append JSON entry with comma
append_json_entry() {
    local entry="$1"
    if [[ "$json_output" != "[" ]]; then
        json_output+=","
    fi
    json_output+=$'\n  '"$entry"
}

# --- GitKraken gpg.exe ---
for version in "${GITKRAKEN_VERSIONS[@]}"; do
    path="/c/Users/<USER>/AppData/Local/gitkraken/app-${version}/resources/app.asar.unpacked/git/usr/bin/gpg.exe"
    if [[ -x "$path" ]]; then
        output=$("$path" --version 2>/dev/null)
        version_line=$(echo "$output" | grep -E '^gpg')
        home_line=$(echo "$output" | grep -E '^Home:')
        append_json_entry "$(jq -n \
            --arg src "GitKraken $version" \
            --arg typ "gpg" \
            --arg ver "$version_line" \
            --arg hm "$home_line" \
            --arg sts "found" \
            '{source: $src, type: $typ, version: $ver, home: $hm, status: $sts}')"
        ((found_count++))
    else
        append_json_entry "$(jq -n \
            --arg src "GitKraken $version" \
            --arg typ "gpg" \
            --arg sts "not found" \
            '{source: $src, type: $typ, status: $sts}')"
        ((not_found_count++))
    fi
done

# --- GitKraken keyboxd.exe ---
for version in "${GITKRAKEN_VERSIONS[@]}"; do
    path="/c/Users/<USER>/AppData/Local/gitkraken/app-${version}/resources/app.asar.unpacked/git/usr/lib/gnupg/keyboxd.exe"
    if [[ -x "$path" ]]; then
        version_line=$("$path" --version 2>/dev/null | head -n 1)
        # Handle empty or invalid version output
        if [[ -z "$version_line" ]]; then
            version_line="Version unavailable"
        fi
        append_json_entry "$(jq -n \
            --arg src "GitKraken $version" \
            --arg typ "keyboxd" \
            --arg ver "$version_line" \
            --arg sts "found" \
            '{source: $src, type: $typ, version: $ver, status: $sts}')"
        ((found_count++))
    else
        append_json_entry "$(jq -n \
            --arg src "GitKraken $version" \
            --arg typ "keyboxd" \
            --arg sts "not found" \
            '{source: $src, type: $typ, status: $sts}')"
        ((not_found_count++))
    fi
done

# --- Standard gpg paths ---
for path in "${GPG_PATHS[@]}"; do
    exe="$path/gpg.exe"
    if [[ -x "$exe" ]]; then
        output=$("$exe" --version 2>/dev/null)
        version_line=$(echo "$output" | grep -E '^gpg')
        home_line=$(echo "$output" | grep -E '^Home:')
        append_json_entry "$(jq -n \
            --arg src "$path" \
            --arg typ "gpg" \
            --arg ver "$version_line" \
            --arg hm "$home_line" \
            --arg sts "found" \
            '{source: $src, type: $typ, version: $ver, home: $hm, status: $sts}')"
        ((found_count++))
    else
        append_json_entry "$(jq -n \
            --arg src "$path" \
            --arg typ "gpg" \
            --arg sts "not found" \
            '{source: $src, type: $typ, status: $sts}')"
        ((not_found_count++))
    fi
done

# --- Standard keyboxd paths ---
for path in "${KEYBOXD_PATHS[@]}"; do
    exe="$path/keyboxd.exe"
    if [[ -x "$exe" ]]; then
        version_line=$("$exe" --version 2>/dev/null | head -n 1)
        # Handle empty or invalid version output
        if [[ -z "$version_line" ]]; then
            version_line="Version unavailable"
        fi
        append_json_entry "$(jq -n \
            --arg src "$path" \
            --arg typ "keyboxd" \
            --arg ver "$version_line" \
            --arg sts "found" \
            '{source: $src, type: $typ, version: $ver, status: $sts}')"
        ((found_count++))
    else
        append_json_entry "$(jq -n \
            --arg src "$path" \
            --arg typ "keyboxd" \
            --arg sts "not found" \
            '{source: $src, type: $typ, status: $sts}')"
        ((not_found_count++))
    fi
done

# Final summary
summary=$(jq -n \
    --arg date "$timestamp" \
    --argjson found $found_count \
    --argjson missing $not_found_count \
    '{summary: {timestamp: $date, found: $found, not_found: $missing}}')

# Add summary as final object
json_output+=","$'\n  '"$summary"
json_output+=$'\n]'

# Save to file
echo "$json_output" > "$logfile"

# Optional pretty output
if command -v jq &>/dev/null; then
    jq . "$logfile" > "${logfile%.json}_pretty.json"
    echo "Saved pretty-printed log to: ${logfile%.json}_pretty.json"
fi

echo "Raw JSON log saved to: $logfile"
echo "Found: $found_count | Not found: $not_found_count"
