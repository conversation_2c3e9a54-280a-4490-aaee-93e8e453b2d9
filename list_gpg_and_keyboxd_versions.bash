#!/bin/bash

echo "< * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * > START of list"

# GitKraken 11.2.0
/c/Users/<USER>/AppData/Local/gitkraken/app-11.2.0/resources/app.asar.unpacked/git/usr/bin/gpg.exe --version
echo "< = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = > \$LOCALAPPDATA/gitkraken/app-11.2.0/.../git/usr/bin"

# GitKraken 11.1.1
/c/Users/<USER>/AppData/Local/gitkraken/app-11.1.1/resources/app.asar.unpacked/git/usr/bin/gpg.exe --version
echo "< = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = > \$LOCALAPPDATA/gitkraken/app-11.1.1/.../git/usr/bin"

# GPG - Program Files
/c/Program\ Files/GnuPG/bin/gpg.exe --version
echo "< = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = > \$ProgramFiles/GnuPG/bin"

/c/Program\ Files\ \(x86\)/GnuPG/bin/gpg.exe --version
echo "< = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = > \$ProgramFiles\(x86\)/GnuPG/bin"

/c/Program\ Files/Git/usr/bin/gpg.exe --version
echo "< = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = > \$ProgramFiles/Git/usr/bin"

/c/msys64/usr/bin/gpg.exe --version
echo "< = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = > \$SystemDrive/msys64/usr/bin"

# keyboxd
/c/Users/<USER>/AppData/Local/gitkraken/app-11.2.0/resources/app.asar.unpacked/git/usr/lib/gnupg/keyboxd.exe --version
echo "< = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = > \$LOCALAPPDATA/gitkraken/app-11.2.0/.../gnupg"

/c/Users/<USER>/AppData/Local/gitkraken/app-11.1.1/resources/app.asar.unpacked/git/usr/lib/gnupg/keyboxd.exe --version
echo "< = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = > \$LOCALAPPDATA/gitkraken/app-11.1.1/.../gnupg"

/c/Program\ Files/GnuPG/bin/keyboxd.exe --version
echo "< = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = > \$ProgramFiles/GnuPG/bin"

/c/Program\ Files\ \(x86\)/GnuPG/bin/keyboxd.exe --version
echo "< = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = > \$ProgramFiles\(x86\)/GnuPG/bin"

/c/Program\ Files/Git/usr/lib/gnupg/keyboxd.exe --version
echo "< = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = > \$ProgramFiles/Git/usr/lib/gnupg"

echo "< * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * > END of list"
