# Select-OhMyPoshTheme.ps1
# Script to select and set Oh My Posh themes from <PERSON>'s collection

param(
    [switch]$ShowCurrent
)

# Path to the theme collection JSON file
$jsonPath = "$env:USERPROFILE\oh-my-posh-theme-collection.json"

# Check if J<PERSON><PERSON> file exists
if (-not (Test-Path $jsonPath)) {
    Write-Host "Theme collection file not found at: $jsonPath" -ForegroundColor Red
    exit 1
}

# Load the JSON file
try {
    $themes = Get-Content $jsonPath | ConvertFrom-Json
} catch {
    Write-Host "Error reading theme collection file: $_" -ForegroundColor Red
    exit 1
}

# Show current theme if requested
if ($ShowCurrent) {
    $currentTheme = [Environment]::GetEnvironmentVariable("POSH_THEME_NAME", "User")
    if ($currentTheme) {
        $current = $themes.themes | Where-Object { $_.file -eq $currentTheme }
        if ($current) {
            Write-Host "`nCurrent theme: " -NoNewline -ForegroundColor Cyan
            Write-Host "$($current.name)" -ForegroundColor Yellow
            Write-Host "Description: $($current.description)" -ForegroundColor Gray
        } else {
            Write-Host "`nCurrent theme: $currentTheme (not in collection)" -ForegroundColor Yellow
        }
    } else {
        Write-Host "`nNo theme currently set in POSH_THEME_NAME variable" -ForegroundColor Red
    }
    Write-Host ""
}

# Display header
$separator = "=" * 60
Write-Host $separator -ForegroundColor Cyan
Write-Host "Tony's Oh My Posh Theme Selector" -ForegroundColor Cyan
Write-Host $separator -ForegroundColor Cyan
Write-Host ""

# Display all themes
Write-Host "Available themes:" -ForegroundColor Green
Write-Host ""

$themes.themes | Sort-Object rank | ForEach-Object {
    $styleColor = switch ($_.style) {
        "detailed" { "Magenta" }
        "balanced" { "Green" }
        "elegant" { "Blue" }
        "compact" { "Yellow" }
        "professional" { "Cyan" }
        "fun" { "Red" }
        "informative" { "DarkYellow" }
        default { "White" }
    }
    
    $rankText = "{0,2}. " -f $_.rank
    $nameText = "{0,-18}" -f $_.name
    $descText = " - {0}" -f $_.description
    
    Write-Host $rankText -NoNewline -ForegroundColor White
    Write-Host $nameText -NoNewline -ForegroundColor $styleColor
    Write-Host $descText -ForegroundColor Gray
}

Write-Host ""
Write-Host "Current theme: " -NoNewline -ForegroundColor Cyan
$currentTheme = [Environment]::GetEnvironmentVariable("POSH_THEME_NAME", "User")
if ($currentTheme) {
    $current = $themes.themes | Where-Object { $_.file -eq $currentTheme }
    if ($current) {
        Write-Host "$($current.name)" -ForegroundColor Yellow
    } else {
        Write-Host "$currentTheme" -ForegroundColor Yellow
    }
} else {
    Write-Host "None set" -ForegroundColor Red
}

Write-Host ""

# Get user selection
do {
    $selection = Read-Host "Enter theme number (1-$($themes.themes.Count)) or 'q' to quit"
    
    if ($selection -eq 'q' -or $selection -eq 'Q') {
        Write-Host "Exiting..." -ForegroundColor Yellow
        exit 0
    }
    
    # Try to parse as integer
    $themeNumber = 0
    if ([int]::TryParse($selection, [ref]$themeNumber)) {
        $selectedTheme = $themes.themes | Where-Object { $_.rank -eq $themeNumber }
        
        if ($selectedTheme) {
            # Update the environment variable
            [Environment]::SetEnvironmentVariable("POSH_THEME_NAME", $selectedTheme.file, "User")
            
            Write-Host ""
            Write-Host "✓ Theme updated successfully!" -ForegroundColor Green
            Write-Host "Selected: " -NoNewline -ForegroundColor Cyan
            Write-Host "$($selectedTheme.name)" -ForegroundColor Yellow
            Write-Host "File: $($selectedTheme.file)" -ForegroundColor Gray
            Write-Host ""
            Write-Host "To apply the new theme:" -ForegroundColor Cyan
            Write-Host "1. Restart PowerShell, OR run refreshenv.cmd (if Chocolatey is installed)" -ForegroundColor White
            Write-Host "2. Run: . `$PROFILE" -ForegroundColor White
            Write-Host "3. Preview with: oh-my-posh print primary --config `"`$env:POSH_THEMES_PATH\`$env:POSH_THEME_NAME`"" -ForegroundColor White
            
            break
        } else {
            Write-Host "Invalid selection. Please enter a number between 1 and $($themes.themes.Count)." -ForegroundColor Red
        }
    } else {
        Write-Host "Invalid input. Please enter a number or 'q' to quit." -ForegroundColor Red
    }
} while ($true)
