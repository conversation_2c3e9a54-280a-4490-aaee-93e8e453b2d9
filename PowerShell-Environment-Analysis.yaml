---
name: "PowerShell Environment Analysis + Docs"
description: "Complete PowerShell environment analysis with documentation references for PATH and PSModulePath configuration"
tags: ["powershell", "environment", "diagnostics", "documentation"]
author: "Tony"
version: "1.0"

command: |
  Write-Host "=== PowerShell Environment Analysis ===" -ForegroundColor Cyan
  Write-Host ""
  
  Write-Host "1. SYSTEM PATH (PowerShell entries):" -ForegroundColor Yellow
  [System.Environment]::GetEnvironmentVariable("PATH", "Machine") -split ';' | Where-Object { $_ -match 'PowerShell' } | ForEach-Object { Write-Host "   $_" }
  Write-Host ""
  
  Write-Host "2. CURRENT RUNTIME PATH (PowerShell entries):" -ForegroundColor Yellow
  $env:PATH -split ';' | Where-Object { $_ -match 'PowerShell' } | ForEach-Object { Write-Host "   $_" }
  Write-Host ""
  
  Write-Host "3. PSModulePath (first 6 entries):" -ForegroundColor Yellow
  $env:PSModulePath -split ';' | Select-Object -First 6 | ForEach-Object { Write-Host "   $_" }
  Write-Host ""
  
  Write-Host "4. PowerShell Version:" -ForegroundColor Yellow
  $PSVersionTable.PSVersion
  Write-Host ""
  
  Write-Host "5. Environment Analysis:" -ForegroundColor Yellow
  $systemPSCore = [System.Environment]::GetEnvironmentVariable("PATH", "Machine") -split ';' | Where-Object { $_ -match 'PowerShell\\7' }
  $runtimePSCore = $env:PATH -split ';' | Where-Object { $_ -match 'PowerShell\\7' }
  
  if ($systemPSCore) {
    Write-Host "   ⚠️  WARNING: PowerShell Core found in System PATH (may be redundant)" -ForegroundColor Red
    $systemPSCore | ForEach-Object { Write-Host "      $_" -ForegroundColor Red }
  } else {
    Write-Host "   ✅ System PATH clean - no redundant PowerShell Core entries" -ForegroundColor Green
  }
  
  if ($runtimePSCore) {
    Write-Host "   ℹ️  PowerShell Core auto-managed in runtime PATH" -ForegroundColor Blue
  }
  
  Write-Host ""
  Write-Host "=== Documentation References ===" -ForegroundColor Cyan
  Write-Host ""
  Write-Host "PSModulePath Management:" -ForegroundColor Yellow
  Write-Host "• about_PSModulePath: https://docs.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_psmodulepath"
  Write-Host "• Installing PowerShell: https://docs.microsoft.com/en-us/powershell/scripting/install/installing-powershell-on-windows"
  Write-Host ""
  Write-Host "Environment Variables:" -ForegroundColor Yellow
  Write-Host "• about_Environment_Variables: https://docs.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_environment_variables"
  Write-Host "• Windows Environment Variables: https://docs.microsoft.com/en-us/windows/win32/procthread/environment-variables"
  Write-Host ""
  Write-Host "PowerShell Core:" -ForegroundColor Yellow
  Write-Host "• GitHub Repository: https://github.com/PowerShell/PowerShell"
  Write-Host "• Migration Guide: https://docs.microsoft.com/en-us/powershell/scripting/whats-new/migrating-from-windows-powershell-51-to-powershell-7"
  Write-Host ""
  Write-Host "=== Key Recommendations ===" -ForegroundColor Cyan
  Write-Host "• Let PowerShell Core auto-manage its executable PATH"
  Write-Host "• Let PowerShell Core auto-construct PSModulePath"
  Write-Host "• Only add custom module paths to User/System PSModulePath"
  Write-Host "• Keep Windows PowerShell PATH entries for compatibility"

arguments: []

shell: "pwsh"
