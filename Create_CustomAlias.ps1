# Create a symlink to an app and add to custom PATH folder
$targetApp = Read-Host "Enter full path to target app (e.g., 'C:\Tools\MyApp.exe')"
$aliasName = Read-Host "Enter alias name (e.g., 'myapp.exe')"
$customBin = "$env:USERPROFILE\bin"

# Ensure bin directory exists
if (-not (Test-Path $customBin)) {
    New-Item -ItemType Directory -Path $customBin
}

$aliasPath = Join-Path $customBin $aliasName

# Create symbolic link (requires admin)
cmd /c mklink `"$aliasPath`" `"$targetApp`"

# Advise user to add folder to PATH if not already
$path = [System.Environment]::GetEnvironmentVariable("Path", "User")
if (-not $path.Split(';') -contains $customBin) {
    Write-Host "`n⚠️ '$customBin' is not in your PATH. Run the following to add it:"
    Write-Host "[System.Environment]::SetEnvironmentVariable('Path', `"$path;$customBin`", 'User')" -ForegroundColor Yellow
} else {
    Write-Host "Alias created and folder is in PATH. You can now use '$aliasName' from terminal."
}
