@echo off
echo Checking Python installation...

where python
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Python not found in PATH.
    exit /b 1
)

python --version
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Failed to run python --version.
    exit /b 1
)

echo ✅ Python is installed and working.
echo Checking pip...

pip --version
if %ERRORLEVEL% NEQ 0 (
    echo ❌ pip is not available.
    exit /b 1
)

echo ✅ pip is working.
echo Listing installed packages...
pip list
